# LSM Hide Module

Module ẩn tiến trình dựa trên LSM hooks. Module này sử dụng Linux Security Module (LSM) hooks để ẩn các tiến trình thuộc một cgroup cụ thể khỏi các công cụ liệt kê tiến trình như `ps`, `top`, `htop`, và các công cụ quản lý tiến trình khác.

## Tính năng

- Ẩn tiến trình dựa trên cgroup ID
- Tích hợp với module `cpu_throttle` thông qua eBPF maps
- Hỗ trợ các tính năng che giấu và quota từ module `cpu_throttle`
- Công cụ dòng lệnh `lsm_ctl` để quản lý module

## Yêu cầu

- Kernel Linux >= 5.8 (khuyến nghị >= 6.8 để hỗ trợ đầy đủ tính năng)
- Hỗ trợ BPF, BTF và LSM
- libbpf và bpftool
- clang và LLVM

## Cài đặt

```bash
make
sudo make install
```

## Sử dụng

### Khởi động module

```bash
# Khởi động module
sudo lsm_hide_loader

# Hoặc cài đặt và bật service
sudo cp lsm_hide.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable --now lsm_hide.service
```

### Sử dụng công cụ điều khiển

`lsm_ctl` là công cụ dòng lệnh để quản lý module LSM Hide:

```bash
# Xem trợ giúp
sudo lsm_ctl

# Liệt kê các cgroup và PID đang được ẩn
sudo lsm_ctl list

# Thêm cgroup vào danh sách ẩn
sudo lsm_ctl add /sys/fs/cgroup/system.slice/container_name

# Xóa cgroup khỏi danh sách ẩn
sudo lsm_ctl remove

# Bật chế độ che giấu (obfuscation)
sudo lsm_ctl obfuscate 1

# Tắt chế độ che giấu
sudo lsm_ctl obfuscate 0

# Hiển thị thống kê
sudo lsm_ctl stats

# Xóa tất cả PID khỏi danh sách ẩn
sudo lsm_ctl clear

# Đặt quota cho cgroup
sudo lsm_ctl quota <cgid> <bytes>
```

## Tích hợp với module cpu_throttle

Module LSM Hide tích hợp với module `cpu_throttle` thông qua cơ chế chia sẻ maps. Các maps được chia sẻ bao gồm:

- `quota_cg`: Quota CPU cho mỗi cgroup
- `obfuscation_flag`: Cờ bật/tắt chế độ che giấu
- `events`: Map ghi lại các sự kiện

## Cấu trúc mã nguồn

- `lsm_hide.bpf.c`: Mã nguồn eBPF chứa các LSM hooks
- `lsm_hide_loader.c`: Chương trình nạp module eBPF
- `lsm_ctl.c`: Công cụ dòng lệnh để quản lý module
- `Makefile`: File build
- `lsm_hide.service`: File service systemd

## Giấy phép

Dự án này được phân phối dưới giấy phép MIT. 