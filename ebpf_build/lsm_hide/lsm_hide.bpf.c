#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include "lsm_defs.h"

char LICENSE[] SEC("license") = "Dual MIT/GPL";

/* =====================================================
 *  Maps
 * ===================================================== */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 64);
    __type(key, u32);
    __type(value, u64);
} target_cgrp_id SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, u32);
    __type(value, u32);
} hidden_pids SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 64);
    __type(key, u64);
    __type(value, u64);
} quota_cg SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, u32);
} obfuscation_flag SEC(".maps");

/* =====================================================
 *  Cấu trúc sự kiện để gửi thông báo chi tiết
 * ===================================================== */
struct event {
    u64 cgroup_id;     /* ID của cgroup */
    u32 pid;           /* PID của tiến trình */
    u32 tgid;          /* TGID của tiến trình */
    u32 event_type;    /* Loại sự kiện (1=proc access, 2=task query, 3=syscall block) */
    u64 timestamp;     /* Thời gian xảy ra */
};

/* -----------------------------------------------------
 * Helper: so khớp cgroup hiện tại với giá trị trong map
 * ----------------------------------------------------- */
static __always_inline bool is_hidden_cgroup(void)
{
    u32 k = 0;
    u64 *val = bpf_map_lookup_elem(&target_cgrp_id, &k);
    if (!val)
        return false;
    u64 cg_id = bpf_get_current_cgroup_id();
    return cg_id == *val;
}

/* -----------------------------------------------------
 * Helper: kiểm tra PID có trong danh sách ẩn
 * ----------------------------------------------------- */
static __always_inline bool is_hidden_pid(u32 pid)
{
    return bpf_map_lookup_elem(&hidden_pids, &pid) != NULL;
}

/* -----------------------------------------------------
 * Helper: kiểm tra và cập nhật quota cho cgroup
 * ----------------------------------------------------- */
static __always_inline bool check_quota(u64 cgroup_id, u32 amount)
{
    u64 *quota = bpf_map_lookup_elem(&quota_cg, &cgroup_id);
    if (!quota)
        return true; /* Không có quota, cho phép */
    
    /* Nếu quota không đủ */
    if (*quota < amount)
        return false;
    
    /* Giảm quota */
    __sync_fetch_and_sub(quota, amount);
    return true;
}

/* -----------------------------------------------------
 * Helper: kiểm tra cờ obfuscation
 * ----------------------------------------------------- */
static __always_inline bool should_obfuscate(void)
{
    u32 key = 0;
    u32 *flag = bpf_map_lookup_elem(&obfuscation_flag, &key);
    return flag && (*flag == 1);
}

/* -----------------------------------------------------
 * Helper: thêm PID vào danh sách ẩn
 * ----------------------------------------------------- */
static __always_inline void add_hidden_pid(u32 pid)
{
    u64 now = bpf_ktime_get_ns();
    bpf_map_update_elem(&hidden_pids, &pid, &now, BPF_ANY);
}

/* -----------------------------------------------------
 * Helper: gửi sự kiện chi tiết
 * ----------------------------------------------------- */
static __always_inline void submit_detailed_event(u32 event_type)
{
    struct event *e;
    e = bpf_ringbuf_reserve(&events, sizeof(*e), 0);
    if (e) {
        e->cgroup_id = bpf_get_current_cgroup_id();
        e->pid = bpf_get_current_pid_tgid() >> 32;
        e->tgid = bpf_get_current_pid_tgid() & 0xFFFFFFFF;
        e->event_type = event_type;
        e->timestamp = bpf_ktime_get_ns();
        bpf_ringbuf_submit(e, 0);
    }
}

/* =====================================================
 * Hook [security_inode_permission]
 * -----------------------------------------------------
 *  Từ chối truy cập /proc/* đối với tiến trình thuộc
 *  cgroup ẩn → trả về -ENOENT để giả lập "không tồn tại".
 * ===================================================== */
SEC("lsm.s/inode_permission")
int BPF_PROG(hide_proc_inode, struct inode *inode, int mask)
{
    /* Chỉ can thiệp nếu là tiến trình cần ẩn */
    if (!is_hidden_cgroup())
        return 0;

    /* Kiểm tra hệ thống tệp procfs thông qua magic number */
    u32 magic = 0;
    bpf_core_read(&magic, sizeof(magic), &inode->i_sb->s_magic);
    if (magic == PROC_SUPER_MAGIC) {
        /* Kiểm tra quota trước khi ẩn */
        u64 cgroup_id = bpf_get_current_cgroup_id();
        if (!check_quota(cgroup_id, 1)) {
            /* Nếu hết quota, không ẩn nữa */
            return 0;
        }
        
        /* Kiểm tra cờ obfuscation */
        if (!should_obfuscate()) {
            /* Nếu obfuscation bị tắt, không ẩn */
            return 0;
        }
        
        /* Thêm PID hiện tại vào danh sách ẩn */
        u32 pid = bpf_get_current_pid_tgid() >> 32;
        add_hidden_pid(pid);
        
        /* Gửi sự kiện chi tiết */
        submit_detailed_event(1); /* 1 = proc access */
        
        return -ENOENT;
    }

    return 0; /* Cho phép các inode khác */
}

/* =====================================================
 * Hook [task_getattr]
 * -----------------------------------------------------
 *  Ngăn truy vấn thuộc tính tiến trình (readlink /proc/<pid>,
 *  lstat, v.v.) khi tiến trình đích nằm trong cgroup ẩn.
 * ===================================================== */
SEC("lsm.s/task_getattr")
int BPF_PROG(hide_task_attr, struct task_struct *task)
{
    u32 k = 0;
    u64 *val = bpf_map_lookup_elem(&target_cgrp_id, &k);
    if (!val)
        return 0;

    /* Lấy cgroupid của task đích thông qua helper (>= 6.8) */
#ifdef bpf_task_cgroup_id
    u64 target_cg_id = bpf_task_cgroup_id(task);
    if (target_cg_id != *val)
        return 0;
    
    /* Kiểm tra quota trước khi ẩn */
    if (!check_quota(target_cg_id, 1)) {
        return 0;
    }
#else
    /* Fallback: không có helper → bỏ qua */
    return 0;
#endif
    
    /* Kiểm tra cờ obfuscation */
    if (!should_obfuscate()) {
        return 0;
    }
    
    /* Gửi sự kiện chi tiết */
    submit_detailed_event(2); /* 2 = task query */

    return -ENOENT;
}

/* =====================================================
 * Hook [task_prctl] – Chặn debug & core-dump đối với PID ẩn
 * ===================================================== */
SEC("lsm.s/task_prctl")
int BPF_PROG(block_prctl, int option, unsigned long arg2, unsigned long arg3,
             unsigned long arg4, unsigned long arg5)
{
    /* Lấy PID hiện tại */
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    
    /* Bỏ qua nếu PID không nằm trong danh sách ẩn */
    if (!is_hidden_pid(pid))
        return 0;

    /* Kiểm tra quota trước khi chặn */
    u64 cgroup_id = bpf_get_current_cgroup_id();
    if (!check_quota(cgroup_id, 1)) {
        return 0;
    }
    
    /* Kiểm tra cờ obfuscation */
    if (!should_obfuscate()) {
        return 0;
    }

    if (option == PR_SET_PTRACER || option == PR_SET_DUMPABLE) {
        /* Gửi sự kiện chi tiết */
        submit_detailed_event(3); /* 3 = syscall block */
        return -EPERM;
    }

    return 0;
}

/* =====================================================
 * Hook [task_free] – Dọn PID khi task kết thúc
 * ===================================================== */
SEC("lsm.s/task_free")
int BPF_PROG(cleanup_pid, struct task_struct *task)
{
    u32 pid = BPF_CORE_READ(task, pid);
    bpf_map_delete_elem(&hidden_pids, &pid);
    return 0;
}

/* =====================================================
 * Hook [cred_prepare] – Ẩn UID/GID thật của PID ẩn
 * ===================================================== */
SEC("lsm.s/cred_prepare")
int BPF_PROG(mask_cred, struct cred *new, const struct cred *old, gfp_t gfp)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    if (!is_hidden_pid(pid))
        return 0;

    /* Kiểm tra quota và obfuscation */
    u64 cgroup_id = bpf_get_current_cgroup_id();
    if (!check_quota(cgroup_id, 1) || !should_obfuscate())
        return 0;

    const u32 fake_id = 1000;
    bpf_core_write(&new->uid.val,  fake_id);
    bpf_core_write(&new->gid.val,  fake_id);
    bpf_core_write(&new->euid.val, fake_id);
    bpf_core_write(&new->egid.val, fake_id);
    return 0;
}

/* =====================================================
 * Hook [seccomp] – Từ chối syscall nguy hiểm cho PID ẩn
 * ===================================================== */
SEC("lsm.s/seccomp")
int BPF_PROG(seccomp_guard, struct seccomp_data *sd, long ret)
{
    u32 pid = bpf_get_current_pid_tgid() >> 32;
    if (!is_hidden_pid(pid))
        return 0;

    /* Kiểm tra quota và obfuscation */
    u64 cgroup_id = bpf_get_current_cgroup_id();
    if (!check_quota(cgroup_id, 1) || !should_obfuscate())
        return 0;

    const int deny[3] = { __NR_ptrace, __NR_perf_event_open, __NR_bpf };
#pragma unroll
    for (int i = 0; i < 3; i++) {
        if (sd->nr == deny[i]) {
            /* Gửi sự kiện chi tiết */
            submit_detailed_event(3); /* 3 = syscall block */
            return -EPERM;
        }
    }
    return 0;
} 