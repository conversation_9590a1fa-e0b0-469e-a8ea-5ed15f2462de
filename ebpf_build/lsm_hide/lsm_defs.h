/* 
 * lsm_defs.h - <PERSON><PERSON><PERSON> nghĩa bổ sung cho LSM BPF
 * Ch<PERSON><PERSON> các hằng số và macro cần thiết cho lsm_hide.bpf.c
 */

#ifndef __LSM_DEFS_H
#define __LSM_DEFS_H

/* <PERSON><PERSON><PERSON> nghĩa macro BPF_PROG cho các LSM hooks - đơn giản hóa */
#undef BPF_PROG
#define BPF_PROG(name, ...) name(__VA_ARGS__)

/* Các hằng số magic number cho filesystem */
#ifndef PROC_SUPER_MAGIC
#define PROC_SUPER_MAGIC 0x9fa0
#endif

/* Các mã lỗi */
#ifndef ENOENT
#define ENOENT 2
#endif

#ifndef EPERM
#define EPERM 1
#endif

/* Các hằng số cho prctl */
#ifndef PR_SET_PTRACER
#define PR_SET_PTRACER 0x59616d61
#endif

#ifndef PR_SET_DUMPABLE
#define PR_SET_DUMPABLE 4
#endif

/* Các syscall number */
#ifndef __NR_ptrace
#define __NR_ptrace 101
#endif

#ifndef __NR_perf_event_open
#define __NR_perf_event_open 298
#endif

#ifndef __NR_bpf
#define __NR_bpf 321
#endif

/* Helper macro cho BPF_CORE_READ */
#ifndef BPF_CORE_READ
#define BPF_CORE_READ(src, a) \
    bpf_core_read(&(a), sizeof(a), &(src)->a)
#endif

/* Định nghĩa bpf_core_write nếu không có */
#ifndef bpf_core_write
#define bpf_core_write(dst, src) \
    bpf_probe_write_user(dst, &(src), sizeof(src))
#endif

#endif /* __LSM_DEFS_H */ 