CLANG ?= clang
CFLAGS ?= -g -O2 -Wall
LDLIBS += -lelf -lbpf

TARGETS = lsm_hide_loader lsm_ctl

BPFTOOL ?= bpftool
LIBBPF_SRC := $(shell $(BPFTOOL) btf dump file /sys/kernel/btf/vmlinux format c > vmlinux.h 2> /dev/null && echo 1 || echo 0)

# Đường dẫn đến thư mục output
OUTPUT_DIR = output/obj

.PHONY: all
all: prepare $(TARGETS)

.PHONY: prepare
prepare:
	mkdir -p $(OUTPUT_DIR)
	$(CLANG) -g -O2 -target bpf -D__TARGET_ARCH_x86_64 -I. -c lsm_hide.bpf.c -o $(OUTPUT_DIR)/lsm_hide.bpf.o
	$(BPFTOOL) gen skeleton $(OUTPUT_DIR)/lsm_hide.bpf.o > $(OUTPUT_DIR)/lsm_hide.skel.h

.PHONY: clean
clean:
	rm -f *.o $(TARGETS)
	rm -rf output

# Biên dịch loader
lsm_hide_loader: lsm_hide_loader.c
	$(CC) $(CFLAGS) -I. -I$(OUTPUT_DIR) -o $@ $< $(LDLIBS)

# Biên dịch công cụ điều khiển
lsm_ctl: lsm_ctl.c
	$(CC) $(CFLAGS) -o $@ $< $(LDLIBS)

# Cài đặt
.PHONY: install
install: all
	@echo "Cài đặt LSM Hide..."
	install -m 0755 lsm_hide_loader /usr/local/sbin/
	install -m 0755 lsm_ctl /usr/local/sbin/
	@echo "Đã hoàn tất cài đặt LSM Hide."

# Gỡ cài đặt
.PHONY: uninstall
uninstall:
	@echo "Gỡ cài đặt LSM Hide..."
	rm -f /usr/local/sbin/lsm_hide_loader
	rm -f /usr/local/sbin/lsm_ctl
	@echo "Đã hoàn tất gỡ cài đặt LSM Hide."

# Kiểm tra phụ thuộc
check-deps:
	@echo "Checking dependencies..."
	@which $(CLANG) >/dev/null || (echo "Error: clang not found"; exit 1)
	@which $(BPFTOOL) >/dev/null || (echo "Error: bpftool not found"; exit 1)
	@test -d /usr/include/bpf || (echo "Error: libbpf headers not found"; exit 1)
	@test -f /sys/kernel/btf/vmlinux || (echo "Warning: BTF not available in kernel"; exit 0)

.PHONY: all clean check-deps install uninstall 